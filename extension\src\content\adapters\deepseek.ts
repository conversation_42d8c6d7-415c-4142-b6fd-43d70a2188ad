import { AIAdapter } from './base'
import type { Conversation, Message } from '@/types'

export class DeepSeekAdapter extends AIAdapter {
  platformName = 'DeepSeek'
  platform = 'deepseek' as const

  selectors = {
    inputField: 'textarea[placeholder*="输入"], textarea[placeholder*="Enter"]',
    sendButton: 'button[type="submit"], button[aria-label*="发送"], button[aria-label*="Send"]',
    messageContainer: '.message, [data-role="user"], [data-role="assistant"]'
  }

  async injectPrompt(prompt: string): Promise<void> {
    const inputElement = await this.waitForElement(this.selectors.inputField) as HTMLTextAreaElement
    if (!inputElement) {
      throw new Error('DeepSeek input field not found')
    }

    this.simulateUserInput(inputElement, prompt)
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = document.querySelectorAll(this.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []
      
      messageElements.forEach((element, index) => {
        // DeepSeek可能使用不同的类名或属性来区分用户和助手消息
        const isUser = element.classList.contains('user') || 
                      element.getAttribute('data-role') === 'user' ||
                      element.querySelector('.user-message') !== null

        const contentElement = element.querySelector('.message-content') || 
                              element.querySelector('.content') || 
                              element

        if (contentElement) {
          const content = contentElement.textContent?.trim() || ''
          if (content) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      const title = `DeepSeek对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `deepseek-${Date.now()}`,
        platform: 'deepseek',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('Extract DeepSeek conversation error:', error)
      return null
    }
  }

  isValidPage(): boolean {
    return window.location.hostname === 'chat.deepseek.com'
  }
}
