# EchoSync Chrome插件开发计划文档

## 📋 项目概述

**项目名称**: EchoSync（回声同步）  
**项目类型**: Chrome浏览器插件  
**核心功能**: AI聊天平台提示词同步、历史记录管理、跨设备数据同步  

## 🏗️ 技术架构

### 核心技术栈
- **构建工具**: Vite + `vite-plugin-crx`
- **前端框架**: React 18 + TypeScript
- **样式系统**: Tailwind CSS
- **UI组件库**: shadcn/ui
- **状态管理**: Zustand
- **路由管理**: React Router
- **测试框架**: Jest + React Testing Library

### 项目结构
```
echosync-extension/
├── public/
│   ├── icons/                    # 插件图标
│   └── manifest.json            # MV3 配置文件
├── src/
│   ├── background/              # Service Worker
│   │   └── index.ts
│   ├── content/                 # 内容脚本
│   │   ├── index.ts
│   │   └── adapters/           # AI平台适配器
│   ├── popup/                  # 弹窗页面
│   │   ├── App.tsx
│   │   ├── components/
│   │   ├── hooks/
│   │   └── pages/
│   ├── options/                # 设置页面
│   │   ├── App.tsx
│   │   ├── components/
│   │   └── pages/
│   ├── components/             # 共享组件
│   │   └── ui/                # shadcn/ui组件
│   ├── lib/                   # 工具库
│   │   ├── utils.ts
│   │   ├── storage.ts
│   │   └── sync.ts
│   ├── hooks/                 # 自定义Hooks
│   ├── types/                 # TypeScript类型定义
│   └── styles/               # 全局样式
├── tests/                    # 测试文件
├── vite.config.ts           # Vite配置
├── tailwind.config.js       # Tailwind配置
├── tsconfig.json           # TypeScript配置
└── package.json
```

## 📄 Manifest V3 配置

### manifest.json 核心配置
```json
{
  "manifest_version": 3,
  "name": "EchoSync - AI提示词同步器",
  "version": "1.0.0",
  "description": "在多个AI聊天平台间同步提示词，管理对话历史",
  "permissions": [
    "storage",
    "activeTab",
    "scripting",
    "background"
  ],
  "host_permissions": [
    "https://chat.openai.com/*",
    "https://chat.deepseek.com/*",
    "https://claude.ai/*",
    "https://gemini.google.com/*"
  ],
  "background": {
    "service_worker": "background/index.js"
  },
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["content/index.js"]
    }
  ],
  "action": {
    "default_popup": "popup/index.html",
    "default_title": "EchoSync"
  },
  "options_page": "options/index.html"
}
```

## 🎨 UI设计规范

### shadcn/ui 组件配置
- **主题**: 支持明暗双主题
- **颜色方案**: 现代简约风格
- **响应式**: 适配popup小窗口和options页面
- **组件**: Button, Input, Card, Dialog, Select, Tabs等

### Tailwind CSS 配置
```javascript
// tailwind.config.js
module.exports = {
  content: ["./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        // shadcn/ui 标准颜色变量
      }
    }
  },
  plugins: [require("tailwindcss-animate")]
}
```

## 🔧 核心功能模块

### 1. Popup页面 (400x600px)
- **首页**: 快速同步面板
- **历史记录**: 最近提示词列表
- **设置**: 基本配置选项

### 2. Options页面
- **账户管理**: 登录/注册
- **同步设置**: 平台选择和配置
- **历史管理**: 完整历史记录浏览
- **高级设置**: VIP功能配置

### 3. 状态管理 (Zustand)
```typescript
interface AppState {
  // 用户状态
  user: User | null;
  isAuthenticated: boolean;
  
  // 同步状态
  syncEnabled: boolean;
  connectedPlatforms: Platform[];
  
  // 历史记录
  prompts: Prompt[];
  conversations: Conversation[];
  
  // 设置
  settings: Settings;
}
```

## 🧪 测试配置

### Jest + React Testing Library
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest'
  }
}
```

### 测试覆盖范围
- 组件单元测试
- Hook测试
- 集成测试
- E2E测试（Playwright）

## 🌐 未来官网架构

### Next.js 技术栈
- **框架**: Next.js 14 (App Router)
- **样式**: Tailwind CSS + shadcn/ui
- **数据库**: Supabase (PostgreSQL)
- **身份认证**: Supabase Auth
- **支付系统**: Stripe
- **部署**: Vercel

### 功能规划
- 用户注册/登录
- 订阅管理
- 数据同步API
- 用户仪表板
- 支付处理

## ☁️ 部署策略建议

### 推荐: Vercel
**优势**:
- Next.js 原生支持
- 零配置部署
- 全球CDN
- 自动HTTPS
- 免费额度充足

**适用场景**:
- 小型到中型项目
- 快速迭代需求
- 成本控制

### 备选: Cloudflare
**优势**:
- 更低成本
- 边缘计算
- D1数据库集成
- Workers强大

**适用场景**:
- 高并发需求
- 全球用户分布
- 复杂后端逻辑

### 最终建议
**初期**: 使用Vercel，快速上线，专注产品开发  
**扩展期**: 根据用户规模和成本考虑迁移到Cloudflare

## 📦 依赖管理

### 核心依赖
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "zustand": "^4.3.0",
    "@radix-ui/react-*": "latest",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^1.14.0"
  },
  "devDependencies": {
    "@types/chrome": "^0.0.246",
    "@vitejs/plugin-react": "^4.0.0",
    "vite-plugin-crx": "^0.2.0",
    "typescript": "^5.0.0",
    "tailwindcss": "^3.3.0",
    "jest": "^29.0.0",
    "@testing-library/react": "^13.4.0"
  }
}
```

## 🔐 安全与隐私

### 数据保护
- 端到端加密
- 本地数据加密存储
- GDPR合规
- 用户数据控制权

### Chrome安全策略
- Content Security Policy (CSP)
- 最小权限原则
- 安全的消息传递
- 代码混淆保护

## 📈 开发里程碑

### Phase 1: 基础框架 (Week 1-2)
- [ ] 项目初始化和配置
- [ ] 基础UI组件开发
- [ ] Manifest V3配置
- [ ] 基础路由设置

### Phase 2: 核心功能 (Week 3-4)
- [ ] 内容脚本开发
- [ ] 提示词捕获机制
- [ ] 本地存储实现
- [ ] 基础同步功能

### Phase 3: 用户界面 (Week 5-6)
- [ ] Popup页面完善
- [ ] Options页面开发
- [ ] 历史记录管理
- [ ] 设置功能实现

### Phase 4: 测试与优化 (Week 7-8)
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 性能优化
- [ ] 发布准备

## 🚀 版本控制

### GitHub仓库结构
```
echosync/
├── extension/          # Chrome插件代码
├── website/           # Next.js官网
├── docs/             # 文档
├── .github/          # GitHub Actions
└── README.md
```

### 分支策略
- `main`: 生产环境
- `develop`: 开发环境
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复

## 🛠️ 开发环境配置

### Vite配置 (vite.config.ts)
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { crx } from 'vite-plugin-crx'
import path from 'path'

export default defineConfig({
  plugins: [
    react(),
    crx({
      manifest: './public/manifest.json',
      contentScripts: {
        injectCss: true
      }
    })
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },
  build: {
    rollupOptions: {
      input: {
        popup: 'src/popup/index.html',
        options: 'src/options/index.html',
        background: 'src/background/index.ts',
        content: 'src/content/index.ts'
      }
    }
  }
})
```

### TypeScript配置
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    "types": ["chrome", "jest", "@testing-library/jest-dom"]
  }
}
```

## 🎯 核心功能实现细节

### 1. AI平台适配器架构
```typescript
// src/content/adapters/base.ts
export abstract class AIAdapter {
  abstract platformName: string;
  abstract selectors: {
    inputField: string;
    sendButton: string;
    messageContainer: string;
  };

  abstract injectPrompt(prompt: string): Promise<void>;
  abstract extractConversation(): Promise<Conversation>;
  abstract isValidPage(): boolean;
}

// 具体实现
export class ChatGPTAdapter extends AIAdapter {
  platformName = 'ChatGPT';
  selectors = {
    inputField: '#prompt-textarea',
    sendButton: '[data-testid="send-button"]',
    messageContainer: '[data-testid="conversation-turn"]'
  };
  // ... 实现具体方法
}
```

### 2. 消息通信架构
```typescript
// src/lib/messaging.ts
export enum MessageType {
  SYNC_PROMPT = 'SYNC_PROMPT',
  GET_HISTORY = 'GET_HISTORY',
  SAVE_CONVERSATION = 'SAVE_CONVERSATION'
}

export interface Message<T = any> {
  type: MessageType;
  payload: T;
  timestamp: number;
}

// Background Script 消息处理
chrome.runtime.onMessage.addListener((message: Message, sender, sendResponse) => {
  switch (message.type) {
    case MessageType.SYNC_PROMPT:
      handlePromptSync(message.payload);
      break;
    // ... 其他消息处理
  }
});
```

### 3. 存储服务实现
```typescript
// src/lib/storage.ts
export class StorageService {
  // Chrome Storage API 封装
  static async set<T>(key: string, value: T): Promise<void> {
    await chrome.storage.local.set({ [key]: value });
  }

  static async get<T>(key: string): Promise<T | null> {
    const result = await chrome.storage.local.get(key);
    return result[key] || null;
  }

  // 大数据量使用 IndexedDB
  static async saveLargeData(data: any): Promise<void> {
    // IndexedDB 实现
  }
}
```

## 📱 响应式设计规范

### Popup页面布局 (320x600px)
```css
/* 针对Chrome插件popup的特殊样式 */
.popup-container {
  @apply w-80 h-[600px] overflow-hidden;
}

.popup-header {
  @apply h-16 border-b bg-background/95 backdrop-blur;
}

.popup-content {
  @apply flex-1 overflow-y-auto p-4;
}

.popup-footer {
  @apply h-12 border-t bg-background/95;
}
```

### Options页面布局
```css
.options-container {
  @apply min-h-screen bg-background;
}

.options-sidebar {
  @apply w-64 border-r bg-muted/50;
}

.options-main {
  @apply flex-1 p-6;
}
```

## 🔄 状态管理详细设计

### Zustand Store结构
```typescript
// src/stores/app-store.ts
interface AppStore {
  // 用户状态
  user: User | null;
  setUser: (user: User | null) => void;

  // 同步状态
  syncSettings: SyncSettings;
  updateSyncSettings: (settings: Partial<SyncSettings>) => void;

  // 提示词历史
  prompts: Prompt[];
  addPrompt: (prompt: Prompt) => void;
  getPromptsByPlatform: (platform: string) => Prompt[];

  // UI状态
  activeTab: string;
  setActiveTab: (tab: string) => void;

  // 异步操作
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
}

export const useAppStore = create<AppStore>((set, get) => ({
  // 初始状态和方法实现
}));
```

## 🧪 测试策略详细规划

### 单元测试示例
```typescript
// src/components/__tests__/PromptCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { PromptCard } from '../PromptCard';

describe('PromptCard', () => {
  const mockPrompt = {
    id: '1',
    content: 'Test prompt',
    platform: 'ChatGPT',
    timestamp: Date.now()
  };

  it('renders prompt content correctly', () => {
    render(<PromptCard prompt={mockPrompt} />);
    expect(screen.getByText('Test prompt')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const onSelect = jest.fn();
    render(<PromptCard prompt={mockPrompt} onSelect={onSelect} />);

    fireEvent.click(screen.getByRole('button'));
    expect(onSelect).toHaveBeenCalledWith(mockPrompt);
  });
});
```

### E2E测试配置
```typescript
// tests/e2e/popup.spec.ts
import { test, expect } from '@playwright/test';

test('popup opens and displays recent prompts', async ({ page }) => {
  await page.goto('chrome-extension://[extension-id]/popup/index.html');

  await expect(page.locator('[data-testid="recent-prompts"]')).toBeVisible();
  await expect(page.locator('[data-testid="sync-button"]')).toBeEnabled();
});
```

## 🚀 CI/CD 流水线

### GitHub Actions配置
```yaml
# .github/workflows/build-and-test.yml
name: Build and Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - run: npm ci
      - run: npm run test
      - run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: extension-build
          path: dist/
```

## 📊 性能优化策略

### 代码分割
```typescript
// 懒加载大型组件
const HistoryPage = lazy(() => import('./pages/HistoryPage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));

// 在路由中使用
<Suspense fallback={<LoadingSpinner />}>
  <Routes>
    <Route path="/history" element={<HistoryPage />} />
    <Route path="/settings" element={<SettingsPage />} />
  </Routes>
</Suspense>
```

### 内存管理
```typescript
// 限制历史记录数量
const MAX_PROMPTS_IN_MEMORY = 1000;
const MAX_CONVERSATIONS_IN_MEMORY = 100;

// 定期清理过期数据
setInterval(() => {
  cleanupExpiredData();
}, 24 * 60 * 60 * 1000); // 每24小时清理一次
```

这个开发计划文档为EchoSync Chrome插件提供了完整的技术路线图，涵盖了从基础架构到部署策略的所有关键方面，包括详细的代码示例和配置文件。
