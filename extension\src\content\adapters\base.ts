import type { AIPlatform, Conversation } from '@/types'

export abstract class AIAdapter {
  abstract platformName: string
  abstract platform: AIPlatform
  abstract selectors: {
    inputField: string
    sendButton: string
    messageContainer: string
  }

  // 注入提示词到输入框
  abstract injectPrompt(prompt: string): Promise<void>

  // 提取当前对话内容
  abstract extractConversation(): Promise<Conversation | null>

  // 检查当前页面是否有效
  abstract isValidPage(): boolean

  // 等待元素出现
  protected waitForElement(selector: string, timeout = 5000): Promise<Element | null> {
    return new Promise((resolve) => {
      const element = document.querySelector(selector)
      if (element) {
        resolve(element)
        return
      }

      const observer = new MutationObserver((mutations, obs) => {
        const element = document.querySelector(selector)
        if (element) {
          obs.disconnect()
          resolve(element)
        }
      })

      observer.observe(document.body, {
        childList: true,
        subtree: true
      })

      // 超时处理
      setTimeout(() => {
        observer.disconnect()
        resolve(null)
      }, timeout)
    })
  }

  // 模拟用户输入
  protected simulateUserInput(element: HTMLElement, text: string) {
    // 触发focus事件
    element.focus()

    // 设置值
    if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
      const inputElement = element as HTMLInputElement
      inputElement.value = text
      
      // 触发input事件
      const inputEvent = new Event('input', { bubbles: true })
      element.dispatchEvent(inputEvent)
      
      // 触发change事件
      const changeEvent = new Event('change', { bubbles: true })
      element.dispatchEvent(changeEvent)
    } else if (element.contentEditable === 'true') {
      element.textContent = text
      
      // 触发input事件
      const inputEvent = new Event('input', { bubbles: true })
      element.dispatchEvent(inputEvent)
    }

    // 移动光标到末尾
    if (element.contentEditable === 'true') {
      const range = document.createRange()
      const selection = window.getSelection()
      range.selectNodeContents(element)
      range.collapse(false)
      selection?.removeAllRanges()
      selection?.addRange(range)
    }
  }

  // 模拟点击
  protected simulateClick(element: HTMLElement) {
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    })
    element.dispatchEvent(clickEvent)
  }

  // 获取输入框当前内容
  protected getCurrentInput(): string {
    const inputElement = document.querySelector(this.selectors.inputField) as HTMLElement
    if (!inputElement) return ''

    if (inputElement.tagName === 'TEXTAREA' || inputElement.tagName === 'INPUT') {
      return (inputElement as HTMLInputElement).value
    } else if (inputElement.contentEditable === 'true') {
      return inputElement.textContent || ''
    }

    return ''
  }

  // 检查是否可以发送消息
  protected canSendMessage(): boolean {
    const sendButton = document.querySelector(this.selectors.sendButton) as HTMLButtonElement
    return sendButton && !sendButton.disabled
  }
}
