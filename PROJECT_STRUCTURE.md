# EchoSync 项目结构总览

## 📁 完整项目结构

```
echosync/
├── 📄 package.json                    # 根目录包管理文件 (Monorepo)
├── 📄 README.md                       # 项目说明文档
├── 📄 .gitignore                      # Git忽略文件
├── 📄 PROJECT_STRUCTURE.md            # 项目结构说明
├── 📁 .github/                        # GitHub Actions配置
│   └── 📁 workflows/
│       └── 📄 ci.yml                  # CI/CD流水线
├── 📁 doc/                            # 项目文档
│   ├── 📄 1：规划.md                   # 项目规划文档
│   ├── 📄 2：技术栈.md                 # 技术栈选型文档
│   ├── 📄 3：户端技术选型.md           # 客户端技术选型
│   └── 📄 4：Chrome插件开发计划.md     # 开发计划文档
├── 📁 extension/                      # Chrome插件项目
│   ├── 📄 package.json                # 插件依赖管理
│   ├── 📄 vite.config.ts              # Vite构建配置
│   ├── 📄 tsconfig.json               # TypeScript配置
│   ├── 📄 tsconfig.node.json          # Node环境TS配置
│   ├── 📄 tailwind.config.js          # Tailwind CSS配置
│   ├── 📄 postcss.config.js           # PostCSS配置
│   ├── 📄 jest.config.js              # Jest测试配置
│   ├── 📄 .eslintrc.cjs               # ESLint配置
│   ├── 📁 public/                     # 静态资源
│   │   ├── 📄 manifest.json           # Chrome插件清单文件
│   │   └── 📁 icons/                  # 插件图标
│   ├── 📁 src/                        # 源代码
│   │   ├── 📁 types/                  # TypeScript类型定义
│   │   │   └── 📄 index.ts            # 核心类型定义
│   │   ├── 📁 lib/                    # 工具库
│   │   │   ├── 📄 utils.ts            # 通用工具函数
│   │   │   ├── 📄 storage.ts          # 存储服务
│   │   │   └── 📄 messaging.ts        # 消息通信服务
│   │   ├── 📁 stores/                 # 状态管理
│   │   │   └── 📄 app-store.ts        # Zustand主状态
│   │   ├── 📁 background/             # Service Worker
│   │   │   └── 📄 index.ts            # 后台脚本
│   │   ├── 📁 content/                # 内容脚本
│   │   │   ├── 📄 index.ts            # 内容脚本入口
│   │   │   └── 📁 adapters/           # AI平台适配器
│   │   │       ├── 📄 base.ts         # 适配器基类
│   │   │       ├── 📄 chatgpt.ts      # ChatGPT适配器
│   │   │       ├── 📄 deepseek.ts     # DeepSeek适配器
│   │   │       ├── 📄 claude.ts       # Claude适配器
│   │   │       └── 📄 gemini.ts       # Gemini适配器
│   │   ├── 📁 popup/                  # 弹窗页面
│   │   │   ├── 📄 index.html          # 弹窗HTML
│   │   │   ├── 📄 main.tsx            # 弹窗入口
│   │   │   ├── 📄 App.tsx             # 弹窗主组件
│   │   │   ├── 📁 components/         # 弹窗组件
│   │   │   │   └── 📄 Header.tsx      # 头部导航
│   │   │   └── 📁 pages/              # 弹窗页面
│   │   │       └── 📄 HomePage.tsx    # 首页
│   │   ├── 📁 options/                # 设置页面
│   │   │   ├── 📄 index.html          # 设置页HTML
│   │   │   ├── 📄 main.tsx            # 设置页入口
│   │   │   └── 📄 App.tsx             # 设置页主组件
│   │   ├── 📁 components/             # 共享组件
│   │   │   └── 📁 ui/                 # shadcn/ui组件
│   │   │       ├── 📄 button.tsx      # 按钮组件
│   │   │       └── 📄 card.tsx        # 卡片组件
│   │   ├── 📁 styles/                 # 样式文件
│   │   │   └── 📄 globals.css         # 全局样式
│   │   └── 📄 setupTests.ts           # 测试配置
│   └── 📁 tests/                      # 测试文件
└── 📁 website/                        # 官方网站项目
    ├── 📄 package.json                # 网站依赖管理
    ├── 📄 next.config.js              # Next.js配置
    ├── 📄 tailwind.config.js          # Tailwind配置
    ├── 📄 tsconfig.json               # TypeScript配置
    ├── 📄 .env.example                # 环境变量示例
    ├── 📁 src/                        # 源代码
    │   ├── 📁 app/                    # Next.js App Router
    │   │   ├── 📄 layout.tsx          # 根布局
    │   │   ├── 📄 page.tsx            # 首页
    │   │   ├── 📁 auth/               # 认证页面
    │   │   ├── 📁 dashboard/          # 用户仪表板
    │   │   └── 📁 api/                # API路由
    │   ├── 📁 components/             # React组件
    │   │   ├── 📁 ui/                 # shadcn/ui组件
    │   │   ├── 📁 layout/             # 布局组件
    │   │   └── 📁 features/           # 功能组件
    │   ├── 📁 lib/                    # 工具库
    │   │   ├── 📄 supabase.ts         # Supabase客户端
    │   │   ├── 📄 stripe.ts           # Stripe配置
    │   │   └── 📄 utils.ts            # 工具函数
    │   ├── 📁 types/                  # 类型定义
    │   │   ├── 📄 database.ts         # 数据库类型
    │   │   └── 📄 stripe.ts           # Stripe类型
    │   └── 📁 hooks/                  # 自定义Hooks
    └── 📁 public/                     # 静态资源
        ├── 📁 images/                 # 图片资源
        └── 📄 favicon.ico             # 网站图标
```

## 🔧 核心配置文件说明

### Chrome插件配置

| 文件 | 用途 | 关键配置 |
|------|------|----------|
| `manifest.json` | Chrome插件清单 | MV3规范、权限、脚本配置 |
| `vite.config.ts` | 构建配置 | vite-plugin-crx、多入口 |
| `tailwind.config.js` | 样式配置 | shadcn/ui主题、响应式 |
| `jest.config.js` | 测试配置 | jsdom环境、覆盖率 |

### 官方网站配置

| 文件 | 用途 | 关键配置 |
|------|------|----------|
| `next.config.js` | Next.js配置 | App Router、图片域名 |
| `.env.example` | 环境变量模板 | Supabase、Stripe密钥 |
| `tailwind.config.js` | 样式配置 | 与插件保持一致的设计系统 |

## 🚀 开发工作流

### 1. 环境准备
```bash
# 克隆项目
git clone https://github.com/your-username/echosync.git
cd echosync

# 安装依赖
npm run setup
```

### 2. 开发模式
```bash
# 同时启动插件和网站开发服务器
npm run dev

# 或分别启动
npm run dev:extension  # 插件开发
npm run dev:website    # 网站开发
```

### 3. 构建部署
```bash
# 构建所有项目
npm run build

# 运行测试
npm run test

# 代码检查
npm run lint
```

## 📦 技术栈总结

### Chrome插件技术栈
- **框架**: React 18 + TypeScript
- **构建**: Vite + vite-plugin-crx
- **样式**: Tailwind CSS + shadcn/ui
- **状态**: Zustand
- **路由**: React Router
- **测试**: Jest + RTL

### 官方网站技术栈
- **框架**: Next.js 14 (App Router)
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **支付**: Stripe
- **样式**: Tailwind CSS + shadcn/ui
- **部署**: Vercel

## 🎯 下一步开发计划

1. **完善插件核心功能**
   - [ ] 完成popup页面开发
   - [ ] 实现options设置页面
   - [ ] 优化AI平台适配器
   - [ ] 添加单元测试

2. **开发官方网站**
   - [ ] 设计首页和产品页面
   - [ ] 实现用户认证系统
   - [ ] 集成Stripe支付
   - [ ] 构建用户仪表板

3. **部署和发布**
   - [ ] 配置CI/CD流水线
   - [ ] 提交Chrome应用商店
   - [ ] 部署网站到Vercel
   - [ ] 设置域名和SSL

这个项目结构为EchoSync提供了完整的开发框架，支持从插件开发到网站部署的全流程开发。
