# echoAIExtention   

echoAsync 属于echoAI生态的一部分，旨在为用户提供一个更高效、更智能的工作方式。通过集成AI助手，echoAsync可以帮助用户更快地完成任务，提高工作效率。

### 脚手架

请基于doc中的技术栈文档，先帮我打架一个chrome插件开发计划文档，文档同样可存在doc目录，包含以下内容：
- 基于 React 和 Vite 的 Chrome 插件项目结构
- 包含 MV3 规范的 manifest.json 文件
- 支持 TypeScript 的配置
- 包含基本的 popup 页面和 options 页面
- 支持 Tailwind CSS 的样式
- 包含基本的状态管理Zustand 
- 包含基本的路由配置（如使用 React Router）
- ui使用shadcn/ui
- 包含基本的测试框架配置（如 Jest 或 React Testing Library）
- 未来的官网页面使用next.js，具有账号登录等功能，又来存储持久层的数据，以及账号付费体系
- 具有身份认证等持久层
- 未来是部署到vercel还是cloudflare，作为一个小型项目请帮我决策
- github托管