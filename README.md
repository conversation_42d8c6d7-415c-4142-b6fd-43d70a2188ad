# EchoSync - AI提示词同步器

<div align="center">
  <img src="./assets/logo.png" alt="EchoSync Logo" width="120" height="120">

  <h3>在多个AI聊天平台间同步提示词，管理对话历史，提升AI使用效率</h3>

  [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
  [![Chrome Web Store](https://img.shields.io/chrome-web-store/v/your-extension-id)](https://chrome.google.com/webstore/detail/your-extension-id)
  [![Website](https://img.shields.io/website?url=https%3A%2F%2Fechosync.ai)](https://echosync.ai)
</div>

## ✨ 功能特性

### 🔄 智能同步
- **多平台支持**: ChatGPT、DeepSeek、Claude、Gemini等主流AI平台
- **实时同步**: 在一个平台输入，其他平台自动填充
- **历史记录**: 自动保存所有提示词和对话历史

### 🎯 高效管理
- **快速访问**: 快捷键快速打开提示词面板
- **智能分类**: 按平台、时间、标签自动分类
- **收藏功能**: 标记常用提示词，快速复用

### 🌟 增强功能
- **提示词优化**: VIP版本提供AI提示词增强
- **跨设备同步**: 云端同步，多设备无缝切换
- **团队协作**: 企业版支持团队共享提示词库

## 🚀 快速开始

### Chrome插件安装

1. **从Chrome应用商店安装**（推荐）
   ```
   https://chrome.google.com/webstore/detail/echosync/your-extension-id
   ```

2. **手动安装开发版**
   ```bash
   # 克隆项目
   git clone https://github.com/your-username/echosync.git
   cd echosync

   # 安装依赖
   npm run setup

   # 构建插件
   npm run build:extension

   # 在Chrome中加载 extension/dist 目录
   ```

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发环境
npm run dev

# 构建项目
npm run build

# 运行测试
npm run test
```

## 📁 项目结构

```
echosync/
├── extension/              # Chrome插件
│   ├── src/
│   │   ├── background/     # Service Worker
│   │   ├── content/        # 内容脚本
│   │   ├── popup/          # 弹窗页面
│   │   ├── options/        # 设置页面
│   │   ├── components/     # 共享组件
│   │   └── lib/           # 工具库
│   ├── public/            # 静态资源
│   └── tests/             # 测试文件
├── website/               # 官方网站
│   ├── src/
│   │   ├── app/           # Next.js App Router
│   │   ├── components/    # React组件
│   │   ├── lib/          # 工具库
│   │   └── types/        # 类型定义
│   └── public/           # 静态资源
├── docs/                 # 项目文档
└── .github/             # GitHub Actions
```

## 🛠️ 技术栈

### Chrome插件
- **框架**: React 18 + TypeScript
- **构建**: Vite + vite-plugin-crx
- **样式**: Tailwind CSS + shadcn/ui
- **状态管理**: Zustand
- **测试**: Jest + React Testing Library

### 官方网站
- **框架**: Next.js 14 (App Router)
- **数据库**: Supabase (PostgreSQL)
- **身份认证**: Supabase Auth
- **支付**: Stripe
- **部署**: Vercel

## 📖 使用指南

### 基础使用

1. **安装插件**后，访问支持的AI平台
2. **输入提示词**，插件自动捕获并同步
3. **快捷键 Ctrl+Shift+E** 打开提示词面板
4. **选择历史提示词**，一键填充到当前页面

### 高级功能

- **设置同步平台**: 在选项页面选择要同步的AI平台
- **管理提示词**: 添加标签、收藏常用提示词
- **导出数据**: 支持导出提示词和对话历史

## 🔧 开发指南

### 环境要求
- Node.js 18+
- npm 或 yarn
- Chrome 浏览器

### 开发流程

1. **Fork项目**并克隆到本地
2. **创建功能分支**: `git checkout -b feature/your-feature`
3. **开发和测试**: `npm run dev` 启动开发环境
4. **提交代码**: 遵循 [Conventional Commits](https://conventionalcommits.org/)
5. **创建PR**: 详细描述变更内容

### 代码规范
- 使用 ESLint + Prettier 格式化代码
- 编写单元测试覆盖新功能
- 遵循 TypeScript 严格模式

## 🤝 贡献指南

我们欢迎所有形式的贡献！

- 🐛 **报告Bug**: [创建Issue](https://github.com/your-username/echosync/issues)
- 💡 **功能建议**: [功能请求](https://github.com/your-username/echosync/issues)
- 📝 **改进文档**: 提交文档PR
- 🔧 **代码贡献**: 提交功能PR

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🔗 相关链接

- [官方网站](https://echosync.ai)
- [Chrome应用商店](https://chrome.google.com/webstore/detail/your-extension-id)
- [用户文档](https://docs.echosync.ai)
- [API文档](https://api.echosync.ai/docs)

## 💬 联系我们

- 📧 邮箱: <EMAIL>
- 🐦 Twitter: [@EchoSyncAI](https://twitter.com/EchoSyncAI)
- 💬 Discord: [加入社区](https://discord.gg/echosync)

---

<div align="center">
  <p>如果这个项目对你有帮助，请给我们一个 ⭐️</p>
  <p>Made with ❤️ by EchoSync Team</p>
</div>



很好，请根据以上内容生成一个初步的项目结构和配置文件。项目分为两个目录，一个是chrome插件目录，一个是官网目录。

### 启动文档
